# Fix for Unique Constraint Error in Recipe Update

## Problem

When updating a recipe with dietary attributes and categories, the following error occurred:

```json
{
    "status": false,
    "message": "unique_recipe_attributes_composite already exists",
    "errorType": "DUPLICATE_ERROR",
    "error": "Validation error"
}
```

## Root Cause Analysis

1. **Incorrect Attribute Type**: The code was querying for `attribute_type = 'dietary_category'` but the correct type is `'dietary'`

2. **Unique Constraint Violation**: The `mo_recipe_attributes` table has a unique constraint `unique_recipe_attributes_composite` on:
   - `recipe_id`
   - `attributes_id` 
   - `may_contain`
   - `use_default`

3. **Improper Update Strategy**: The original code was trying to delete and recreate records, which could cause constraint violations if the same combination already existed.

## Solution Implemented

### 1. Fixed Attribute Type Query
**Before:**
```sql
SELECT id FROM mo_food_attributes WHERE attribute_type = 'dietary_category'
```

**After:**
```sql
SELECT id FROM mo_food_attributes WHERE attribute_type = 'dietary'
```

### 2. Changed Update Strategy

**Before (Problematic):**
- Delete existing records
- Bulk create new records
- Risk of constraint violations

**After (Fixed):**
- Set existing records to `inactive` status
- For each new attribute:
  - Check if record exists with same constraint fields
  - If exists: Update to `active` status
  - If not exists: Create new record

### 3. Applied Same Fix to Categories

Applied the same pattern to category updates to prevent similar issues:
- Set existing categories to `inactive`
- Update existing or create new categories individually

## Code Changes

### Dietary Attributes Handling

```typescript
// Handle dietary attributes if provided
if (dietary_attributes && dietary_attributes.length > 0) {
  if (isUpdate) {
    // Set existing dietary attributes to inactive first
    const dietaryAttributeIds = await db.sequelize.query(
      `SELECT id FROM mo_food_attributes WHERE attribute_type = 'dietary'`,
      { type: db.sequelize.QueryTypes.SELECT, transaction }
    ).then((results: any[]) => results.map(r => r.id));

    if (dietaryAttributeIds.length > 0) {
      await RecipeAttributes.update(
        { status: RecipeAttributesStatus.inactive, updated_by: userId },
        {
          where: {
            recipe_id: targetRecipe.id,
            attributes_id: { [Op.in]: dietaryAttributeIds }
          },
          transaction
        }
      );
    }
  }

  // Process each dietary attribute individually
  for (const attrId of dietary_attributes) {
    const existingAttribute = await RecipeAttributes.findOne({
      where: {
        recipe_id: targetRecipe.id,
        attributes_id: attrId,
        may_contain: false,
        use_default: false
      },
      transaction
    });

    if (existingAttribute) {
      // Update existing record to active
      await RecipeAttributes.update(
        { status: RecipeAttributesStatus.active, updated_by: userId },
        { where: { id: existingAttribute.id }, transaction }
      );
    } else {
      // Create new record
      await RecipeAttributes.create({
        recipe_id: targetRecipe.id,
        attributes_id: attrId,
        unit_of_measure: null,
        unit: null,
        may_contain: false,
        use_default: false,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: null,
        created_by: userId,
        updated_by: userId,
      }, { transaction });
    }
  }
}
```

### Categories Handling

Similar pattern applied to categories with individual processing instead of bulk operations.

## Testing

### Test Payload
The exact payload that was failing:

```json
{
    "recipe_id": 19,
    "recipe_title": "Basmati Rice",
    "recipe_public_title": "Indian Style Basmati Rices 12346988",
    "recipe_description": "Savory Indian rice flavored with toasted and fragrant whole spices and fried onions...",
    "recipe_preparation_time": 10,
    "recipe_cook_time": 20,
    "has_recipe_public_visibility": true,
    "has_recipe_private_visibility": true,
    "recipe_status": "draft",
    "categories": [4, 11],
    "recipe_complexity_level": "medium",
    "dietary_attributes": [21, 18]
}
```

### Test Script
A test script `test-update-fix.js` has been created to verify the fix works with the exact failing payload.

## Benefits of This Approach

1. **Constraint Safe**: No risk of unique constraint violations
2. **Data Preservation**: Existing records are set to inactive rather than deleted
3. **Audit Trail**: Maintains history of changes through status updates
4. **Performance**: Individual processing allows for proper constraint handling
5. **Consistency**: Same pattern used for both categories and dietary attributes

## Verification Steps

1. ✅ Fixed the attribute type query from `'dietary_category'` to `'dietary'`
2. ✅ Changed from delete/bulk-create to inactive/individual-upsert pattern
3. ✅ Applied same fix to categories handling
4. ✅ Maintained transaction safety and error handling
5. ✅ Created test script to verify the fix

## Expected Result

The update operation should now complete successfully without the unique constraint error, returning:

```json
{
  "status": true,
  "message": "Recipe basic information updated successfully",
  "data": {
    "recipe_id": 19,
    "recipe_slug": "basmati-rice"
  }
}
```
