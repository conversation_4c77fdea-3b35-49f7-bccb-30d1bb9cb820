# Recipe Cost Calculation System

## Overview

This system provides automatic cost calculation and freshness management for recipes based on ingredient costs and unit conversions. It integrates your frontend cost conversion logic with the backend to ensure accurate and up-to-date recipe costs.

## Key Features

### 1. Automatic Cost Conversion
- Supports weight, volume, and piece-based units
- Cross-conversion between weight and volume using density
- Matches your frontend `convertCost` function exactly

### 2. Freshness Tracking
- Tracks when ingredient costs are updated
- Tracks when recipe costs are calculated
- Automatically detects when recipe costs are outdated

### 3. Automatic Updates
- When ingredient costs change, affected recipes are automatically updated
- Manual recalculation endpoint for on-demand updates
- Configurable auto-update behavior

## How It Works

### Ingredient Cost Updates
When an ingredient's `cost_per_unit` is updated:

1. **Timestamp Update**: The `cost_last_updated_at` field is automatically updated
2. **Recipe Detection**: System finds all recipes using this ingredient
3. **Auto-Update**: Recipe costs are automatically recalculated using current conversion rates
4. **Freshness Update**: Recipe `ingredient_costs_updated_at` timestamp is updated

### Recipe Cost Calculation
For each ingredient in a recipe:

1. **Unit Conversion**: Convert from ingredient's base unit to recipe's required unit
2. **Cost Calculation**: Apply conversion factor to ingredient's cost per unit
3. **Total Calculation**: Multiply converted cost by recipe quantity
4. **Recipe Total**: Sum all ingredient costs for total recipe cost

### Freshness Detection
The system compares timestamps to determine freshness:

```
Recipe is outdated if:
  ingredient.cost_last_updated_at > recipe.ingredient_costs_updated_at
```

## API Endpoints

### Manual Cost Recalculation
```
POST /api/v1/private/recipes/recalculate-costs/{id}
```

**Request Body:**
```json
{
  "forceUpdate": false  // Optional: force update even if fresh
}
```

**Response:**
```json
{
  "status": true,
  "message": "Recipe costs updated successfully",
  "data": {
    "recipeId": 123,
    "wasOutdated": true,
    "wasUpdated": true,
    "totalCost": 15.75,
    "freshness": {
      "isOutdated": false,
      "outdatedIngredients": [],
      "lastUpdated": "2024-01-15T10:30:00Z",
      "autoUpdateEnabled": true
    }
  }
}
```

## Configuration

### Auto-Update Settings
You can control auto-update behavior in several ways:

1. **Global Setting**: Enable/disable auto-updates system-wide
2. **Per Request**: Control auto-update in specific operations
3. **Force Update**: Override freshness checks for manual recalculation

### Unit Conversion
The system uses the same conversion factors as your frontend:

```typescript
const conversions = {
  // Weight (base: g)
  g: { type: 'weight', factor: 1 },
  kg: { type: 'weight', factor: 1000 },
  
  // Volume (base: ml)  
  ml: { type: 'volume', factor: 1 },
  l: { type: 'volume', factor: 1000 },
  
  // Piece (base: unit)
  unit: { type: 'piece', factor: 1 },
  dozen: { type: 'piece', factor: 12 }
};
```

## Integration Points

### Frontend Integration
Your frontend cost calculation should remain the same. The backend will:

1. **Sync Conversions**: Use identical conversion factors
2. **Provide Fresh Data**: Ensure costs are always current
3. **Handle Updates**: Automatically update when ingredients change

### Database Schema
Key fields for cost management:

**Ingredients Table:**
- `cost_per_unit`: Current cost per base unit
- `cost_last_updated_at`: When cost was last updated
- `unit_of_measure`: Base unit for this ingredient

**Recipes Table:**
- `ingredient_costs_updated_at`: When recipe costs were last calculated

**Recipe Ingredients Table:**
- `ingredient_cost`: Calculated cost for this ingredient in this recipe
- `ingredient_quantity`: Amount needed
- `ingredient_measure`: Unit of measurement

## Error Handling

The system includes comprehensive error handling:

1. **Missing Units**: Graceful fallback when conversion units are not found
2. **Invalid Conversions**: Prevents incompatible unit conversions
3. **Transaction Safety**: All updates are wrapped in database transactions
4. **Partial Failures**: Individual ingredient failures don't stop the entire process

## Monitoring

### Logs
The system logs important events:
- Ingredient cost updates
- Recipe cost recalculations
- Auto-update triggers
- Conversion errors

### Freshness Indicators
Each recipe response includes freshness information:
```json
{
  "freshness": {
    "costs": {
      "isOutdated": false,
      "outdatedIngredients": [],
      "lastUpdated": "2024-01-15T10:30:00Z",
      "autoUpdateEnabled": true,
      "wasAutoUpdated": true
    }
  }
}
```

## Best Practices

1. **Regular Monitoring**: Check logs for conversion errors or failed updates
2. **Batch Updates**: Use transactions when updating multiple ingredients
3. **Validation**: Ensure ingredient units are properly configured
4. **Testing**: Test cost calculations with known values
5. **Backup**: Regular backups before bulk cost updates

## Troubleshooting

### Common Issues

**Recipe costs not updating:**
- Check if auto-update is enabled
- Verify ingredient units are configured
- Check for conversion errors in logs

**Incorrect cost calculations:**
- Verify conversion factors match frontend
- Check ingredient base units
- Validate recipe quantities and units

**Performance issues:**
- Monitor transaction times
- Consider batch processing for large updates
- Check database indexes on timestamp fields
